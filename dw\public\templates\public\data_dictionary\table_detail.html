{% extends "public/data_dictionary/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
/* 页面布局优化 */
.container-fluid {
    max-width: 95%;
    margin: 0 auto;
    padding: 0 15px;
}

/* 表格容器优化 */
.table-container {
    width: 100%;
    overflow-x: auto;
}

/* 表格宽度优化 */
.ant-table-wrapper {
    width: 100%;
    margin: 0;
}

.ant-table {
    width: 100%;
    table-layout: auto;
}

/* 参考页面样式 */
.ant-tabs-content {
    margin-left: 0%;
}

.ant-tabs-tabpane {
    padding: 20px 0;
}

/* 确保内容区域宽度一致 */
.tab-content {
    width: 100%;
}

.table-title {
    margin-bottom: 20px;
    padding: 0;
}

.breadcrumb {
    margin-bottom: 15px;
    padding: 0;
    background: transparent;
}

/* 标签页样式 */
.nav-tabs {
    border-bottom: 2px solid #0D6EFD;
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 0;
    color: #666;
    font-weight: 500;
    padding: 12px 24px;
    margin-right: 2px;
    background: transparent;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #0D6EFD;
    background: rgba(13, 110, 253, 0.1);
}

.nav-tabs .nav-link.active {
    color: #0D6EFD;
    background: #fff;
    border: none;
    border-bottom: 3px solid #0D6EFD;
    font-weight: 600;
}

.parameter-list {
    list-style: none;
    padding: 0;
    margin: 0;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.parameter-list li {
    display: flex;
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
    align-items: flex-start;
}

.parameter-list li:last-child {
    border-bottom: none;
}

.parameter-list li:first-child {
    background: #fafafa;
    font-weight: bold;
}

.parameter-list .left {
    min-width: 120px;
    color: #666;
    font-weight: 500;
}

.parameter-list .right {
    flex: 1;
    color: #333;
    word-break: break-all;
}

.table-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table-title .anticon {
    color: #eb2f96;
    margin-right: 8px;
    font-size: 16px;
}

.table-title h1 {
    margin: 0;
    font-size: 20px;
    color: #333;
}

.ant-table-wrapper {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.ant-table {
    border-collapse: collapse;
    width: 100%;
}

.ant-table-thead th {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: #262626;
    font-size: 14px;
}

.ant-table-tbody td {
    padding: 10px 12px;
    border-bottom: 1px solid #f0f0f0;
    color: #1f1f1f;
    vertical-align: top;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
}

.ant-table-tbody tr:hover {
    background: #f0f8ff;
}

.ant-table-tbody tr:nth-child(even) {
    background: #fafafa;
}

.ant-table-tbody tr:nth-child(even):hover {
    background: #f0f8ff;
}

/* 表格布局优化 */
.ant-table-fixed {
    width: 100% !important;
    table-layout: fixed;
}

.ant-table-thead th {
    padding: 10px 12px;
    font-size: 13px;
    font-weight: 600;
}

/* 字段名列样式 */
.field-name-cell {
    font-size: 14px;
    color: #1f1f1f;
    font-weight: normal;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
}

/* 数据类型列样式 */
.data-type-cell {
    font-size: 14px;
    color: #1f1f1f;
    font-weight: normal;
    word-wrap: break-word;
    white-space: normal;
}

/* 索引表格样式 */
.index-name-cell {
    font-size: 14px;
    color: #1f1f1f;
    font-weight: normal;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
}

.index-type-cell {
    font-size: 14px;
    color: #1f1f1f;
    font-weight: normal;
    word-wrap: break-word;
    white-space: normal;
}

.index-columns-cell {
    font-size: 14px;
    color: #1f1f1f;
    word-break: break-all;
    font-weight: normal;
    word-wrap: break-word;
    white-space: normal;
}

.index-unique-cell {
    text-align: center;
    font-weight: normal;
    font-size: 14px;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 20px;
}

.breadcrumb-item a {
    color: #1890ff;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.export-buttons {
    margin-top: 20px;
    text-align: right;
}

.export-buttons .btn {
    margin-left: 10px;
}

.field-constraint {
    display: inline-block;
    background: #f0f0f0;
    color: #595959;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 6px;
    font-weight: 500;
    border: 1px solid #d9d9d9;
}

.field-constraint.primary {
    background: #0D6EFD;
    color: #ffffff;
    border-color: #0D6EFD;
}

.field-constraint.unique {
    background: #198754;
    color: #ffffff;
    border-color: #198754;
}

.field-constraint.indexed {
    background: #fd7e14;
    color: #ffffff;
    border-color: #fd7e14;
}

/* 约束标签悬停效果 */
.field-constraint:hover {
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.field-constraint.primary:hover {
    background: #0b5ed7;
}

.field-constraint.unique:hover {
    background: #157347;
}

.field-constraint.indexed:hover {
    background: #e8650e;
}

/* SQL模板卡片样式 */
.sql-template-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.sql-template-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.sql-template-header {
    background: linear-gradient(135deg, #0D6EFD 0%, #0056b3 100%);
    color: white;
    padding: 16px 20px;
    border-bottom: 1px solid #e9ecef;
}

.sql-template-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sql-template-meta {
    font-size: 12px;
    opacity: 0.9;
    margin-top: 4px;
}

.sql-template-body {
    padding: 20px;
}

/* 只在数据字典页面中应用SQL模板样式 - 完全隔离 */
.data-dictionary-page .sql-template-card .sql-template-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.data-dictionary-page .sql-template-card .sql-template-info-item {
    display: flex;
    align-items: flex-start;
}

.data-dictionary-page .sql-template-card .sql-template-info-label {
    min-width: 80px;
    color: #666;
    font-weight: 500;
    margin-right: 10px;
}

.data-dictionary-page .sql-template-card .sql-template-info-value {
    color: #333;
    flex: 1;
}

.sql-template-code {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
}

.sql-template-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 10px;
}

.sql-template-badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 500;
    border-radius: 12px;
    text-transform: uppercase;
}

.badge-type {
    background: #e3f2fd;
    color: #1976d2;
}

.badge-scope {
    background: #f3e5f5;
    color: #7b1fa2;
}

.badge-param {
    background: #fff3e0;
    color: #f57c00;
}

.badge-complete {
    background: #e8f5e8;
    color: #2e7d32;
}

.badge-incomplete {
    background: #ffebee;
    color: #c62828;
}

.no-templates {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

.no-templates i {
    font-size: 48px;
    color: #dee2e6;
    margin-bottom: 15px;
}

.templates-count {
    background: #0D6EFD;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.sql-template-code:hover {
    background: #e9ecef !important;
    border-color: #0D6EFD;
}

/* 复制按钮样式 */
.copy-sql-btn {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.copy-sql-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.copy-sql-btn.btn-success {
    background-color: #198754;
    border-color: #198754;
    color: white;
}

.copy-sql-btn .copy-icon {
    margin-right: 4px;
    font-size: 11px;
}

.copy-sql-btn .copy-text {
    font-size: 11px;
}

/* 滑入动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 注释文本处理样式 */
.comment-cell {
    width: 100%;
    position: relative;
    padding: 8px 12px;
}

.comment-text {
    word-wrap: break-word;
    word-break: break-word;
    line-height: 1.4;
    font-size: 13px;
    width: 100%;
}

.comment-short {
    max-height: 60px;
    overflow: hidden;
    position: relative;
}

.comment-short::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 20px;
    background: linear-gradient(to right, transparent, #fff);
}

.comment-expandable {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 4px;
    border-radius: 4px;
}

.comment-expandable:hover {
    background-color: #f8f9fa;
}

.comment-expanded {
    max-height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    margin: 2px 0;
    white-space: pre-wrap;
    width: 100%;
}

.comment-indicator {
    color: #0D6EFD;
    font-size: 11px;
    font-weight: 500;
    margin-top: 2px;
    cursor: pointer;
}

.comment-indicator:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* SQL模板列表视图样式 */
.sql-template-list {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.sql-template-list-item {
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.sql-template-list-item:last-child {
    border-bottom: none;
}

.sql-template-list-item:hover {
    background-color: #f8f9fa;
}

.sql-template-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.sql-template-list-title {
    font-weight: 600;
    color: #0D6EFD;
    font-size: 14px;
    margin: 0;
}

.sql-template-list-badges {
    display: flex;
    gap: 4px;
}

.sql-template-list-meta {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #6c757d;
}

.sql-template-list-description {
    font-size: 13px;
    color: #495057;
    margin-top: 4px;
    line-height: 1.4;
}

/* 搜索高亮 */
.search-highlight {
    background-color: #fff3cd;
    padding: 1px 2px;
    border-radius: 2px;
}

/* 模板详情模态框样式 */
.modal-xl {
    max-width: 90%;
}

#templateDetailModal .modal-body {
    padding: 1.5rem;
}

#templateDetailModal .sql-template-code {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0;
    overflow: hidden;
}

#templateDetailModal .sql-template-code pre {
    margin: 0;
    background: transparent;
    border: none;
    padding: 1rem;
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.3;
}

#templateDetailModal .sql-template-code code {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.3;
    color: #333;
    display: block;
    padding: 0;
    margin: 0;
}

#templateDetailModal .sql-template-info {
    background: none;
    border-radius: 0;
    padding: 0;
    margin-bottom: 1.5rem;
    display: block !important;
    grid-template-columns: none !important;
}

#templateDetailModal .sql-template-info-item {
    margin-bottom: 0.5rem !important;
    padding: 0.5rem 0 !important;
    background: none !important;
    border: none !important;
    border-radius: 0 !important;
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    min-height: 32px !important;
    flex-direction: row !important;
}

#templateDetailModal .sql-template-info-item:last-child {
    margin-bottom: 0;
}

#templateDetailModal .sql-template-info-label {
    font-weight: 500 !important;
    color: #6c757d !important;
    font-size: 13px !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 80px !important;
    flex-shrink: 0 !important;
    display: flex !important;
    align-items: center !important;
    background: none !important;
    border: none !important;
    border-bottom: none !important;
}

#templateDetailModal .sql-template-info-label::before {
    content: "▸";
    color: #0D6EFD;
    margin-right: 0.4rem;
    font-size: 12px;
    font-weight: normal;
}

#templateDetailModal .sql-template-info-value {
    color: #343a40;
    font-size: 13px;
    line-height: 1.4;
    margin: 0 !important;
    padding: 0 !important;
    flex: 1;
    word-wrap: break-word;
    white-space: pre-wrap;
    background: none !important;
    border: none !important;
    border-radius: 0 !important;
}

/* 描述字段特殊样式 - 移除冲突样式 */
#templateDetailModal .template-description {
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 120px;
    overflow-y: auto;
}

/* 标签组合样式 */
#templateDetailModal .sql-template-info-value .sql-template-badge {
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
    display: inline-block;
}

/* 复制成功通知样式 */
.copy-success-notification {
    position: fixed !important;
    top: 80px !important;
    right: 20px !important;
    z-index: 99999 !important;
    pointer-events: auto !important;
}

.copy-success-notification:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4) !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container-fluid {
        max-width: 98%;
        padding: 0 10px;
    }

    .comment-cell {
        padding: 6px 8px;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        max-width: 100%;
        padding: 0 5px;
    }

    .data-dictionary-page .sql-template-card .sql-template-info {
        grid-template-columns: 1fr;
    }

    .nav-tabs .nav-link {
        padding: 8px 16px;
        font-size: 14px;
    }

    .sql-template-header {
        padding: 12px 16px;
    }

    .sql-template-body {
        padding: 16px;
    }

    .comment-cell {
        padding: 4px 6px;
    }

    .ant-table-thead th {
        padding: 8px 6px;
        font-size: 13px;
    }

    .ant-table-tbody td {
        padding: 8px 6px;
        font-size: 13px;
    }

    .field-name-cell, .data-type-cell, .index-name-cell, .index-type-cell, .index-columns-cell {
        font-size: 13px !important;
    }

    .field-constraint {
        font-size: 12px !important;
        padding: 2px 6px !important;
    }

    /* 移动端表格列宽调整 */
    .ant-table colgroup col:nth-child(1) { width: 8%; }
    .ant-table colgroup col:nth-child(2) { width: 20%; }
    .ant-table colgroup col:nth-child(3) { width: 18%; }
    .ant-table colgroup col:nth-child(4) { width: 14%; }
    .ant-table colgroup col:nth-child(5) { width: 8%; }
    .ant-table colgroup col:nth-child(6) { width: 10%; }
    .ant-table colgroup col:nth-child(7) { width: 10%; }
    .ant-table colgroup col:nth-child(8) { width: 12%; }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 面包屑导航 -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'data_dictionary:database_list' %}">数据字典</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'data_dictionary:database_detail' database.id %}">{{ database.name }}数据库</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ table.name }}表</li>
                </ol>
            </nav>

            <!-- 表标题 -->
            <div class="table-title">
                <i class="anticon anticon-heart-o">♥</i>
                <h1>{{ table.comment|default:table.name }} - {{ table.name }}表</h1>
            </div>

            <!-- 标签页导航 -->
            <ul class="nav nav-tabs" id="tableDetailTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                <i class="fas fa-table"></i> 表概览
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="sql-templates-tab" data-bs-toggle="tab" data-bs-target="#sql-templates" type="button" role="tab" aria-controls="sql-templates" aria-selected="false">
                <i class="fas fa-code"></i> 关联SQL模板
                <span class="templates-count" id="templates-count" style="display: none;"></span>
            </button>
        </li>
    </ul>

    <!-- 标签页内容 -->
    <div class="tab-content" id="tableDetailTabContent">
        <!-- 表概览标签页 -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
            <div class="ant-tabs-content ant-tabs-content-animated">
                <div role="tabpanel" aria-hidden="false" class="ant-tabs-tabpane ant-tabs-tabpane-active">
                    <div>
                <ul class="parameter-list">
                    <li>
                        <div><i class="anticon anticon-heart-o" style="color: unset; cursor: pointer; margin-right: 5px;">♥</i><a>{{ table.comment|default:table.name }}</a></div>
                    </li>
                    <li>
                        <div class="left"><span>表名</span>：</div>
                        <div class="right">{{ table.name }}</div>
                    </li>
                    <li>
                        <div class="left"><span>中文名</span>：</div>
                        <div class="right">{{ table_info.comment }}</div>
                    </li>
                    <li>
                        <div class="left"><span>数据库</span>：</div>
                        <div class="right">{{ table_info.database_name }}</div>
                    </li>
                    <li>
                        <div class="left"><span>表类型</span>：</div>
                        <div class="right">{{ table_info.table_type }}</div>
                    </li>
                    <li>
                        <div class="left"><span>存储引擎</span>：</div>
                        <div class="right">{{ table_info.engine }}</div>
                    </li>

                    <li>
                        <div class="left"><span>描述</span>：</div>
                        <div class="right">{{ table_info.description }}</div>
                    </li>
                    <li>
                        <div class="left"><span>唯一键</span>：</div>
                        <div class="right">{{ table_info.unique_key_fields }}</div>
                    </li>
                    <li>
                        <div class="left"><span>数据来源</span>：</div>
                        <div class="right">{{ table_info.data_source }}</div>
                    </li>
                    <li>
                        <div class="left"><span>更新频率</span>：</div>
                        <div class="right">{{ table_info.update_frequency }}</div>
                    </li>
                    <li>
                        <div class="left"><span>是否废弃</span>：</div>
                        <div class="right">{{ table_info.is_deprecated }}</div>
                    </li>
                    <li>
                        <div class="left"><span>创建时间</span>：</div>
                        <div class="right">{{ table_info.create_time }}</div>
                    </li>
                    <li>
                        <div class="left"><span>最后更新</span>：</div>
                        <div class="right">{{ table_info.update_time }}</div>
                    </li>
                    <li>
                        <div class="left"><span>导出功能</span>：</div>
                        <div class="right">
                            <a href="{% url 'data_dictionary:export_table' database.id table.id %}?type=excel" target="_blank">
                                <span>Excel下载</span><i class="ic ic-download">⬇</i>
                            </a>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <a href="{% url 'data_dictionary:export_table' database.id table.id %}?type=sql" target="_blank">
                                <span>SQL脚本</span><i class="ic ic-sample">📄</i>
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="left"><span>字段信息</span>：</div>
                        <div class="right"></div>
                    </li>
                </ul>

                <!-- 字段信息表格 -->
                <div class="ant-table-wrapper">
                    <div class="ant-spin-nested-loading">
                        <div class="ant-spin-container">
                            <div class="ant-table ant-table-large ant-table-scroll-position-left">
                                <div class="ant-table-content">
                                    <div class="ant-table-scroll">
                                        <div class="ant-table-body" style="overflow-x: auto;">
                                            <table class="ant-table-fixed">
                                                <colgroup>
                                                    <col style="width: 6%;">
                                                    <col style="width: 16%;">
                                                    <col style="width: 14%;">
                                                    <col style="width: 12%;">
                                                    <col style="width: 8%;">
                                                    <col style="width: 12%;">
                                                    <col style="width: 12%;">
                                                    <col style="width: 20%;">
                                                </colgroup>
                                                <thead class="ant-table-thead">
                                                    <tr>
                                                        <th style="text-align: center;"><span>序号</span></th>
                                                        <th><span>字段名</span></th>
                                                        <th><span>中文名</span></th>
                                                        <th><span>数据类型</span></th>
                                                        <th style="text-align: center;"><span>可空</span></th>
                                                        <th><span>默认值</span></th>
                                                        <th style="text-align: center;"><span>约束</span></th>
                                                        <th><span>注释</span></th>
                                                    </tr>
                                                </thead>
                                                <tbody class="ant-table-tbody">
                                                    {% for column in columns %}
                                                    <tr class="ant-table-row ant-table-row-level-0">
                                                        <td style="text-align: center;">
                                                            <span class="ant-table-row-indent indent-level-0" style="padding-left: 0px;"></span>
                                                            <div>{{ forloop.counter }}</div>
                                                        </td>
                                                        <td><div class="field-name-cell">{{ column.name }}</div></td>
                                                        <td>{{ column.comment|default:"" }}</td>
                                                        <td><span class="data-type-cell">{{ column.type }}</span></td>
                                                        <td style="text-align: center;"><span>{{ column.is_nullable|yesno:"是,否" }}</span></td>
                                                        <td>{{ column.default|default:"" }}</td>
                                                        <td style="text-align: center;">
                                                            {% if column.is_primary_key %}
                                                                <span class="field-constraint primary">主键</span>
                                                            {% endif %}
                                                            {% if column.is_unique %}
                                                                <span class="field-constraint unique">唯一</span>
                                                            {% endif %}
                                                            {% if column.is_indexed %}
                                                                <span class="field-constraint indexed">索引</span>
                                                            {% endif %}
                                                            {% if column.is_auto_increment %}
                                                                <span class="field-constraint">自增</span>
                                                            {% endif %}
                                                        </td>
                                                        <td class="comment-cell">
                                                            {% if column.description|length > 100 %}
                                                                <div class="comment-text comment-short comment-expandable"
                                                                     onclick="toggleCommentDisplay(this)"
                                                                     title="点击查看完整注释">
                                                                    {{ column.description }}
                                                                </div>
                                                                <div class="comment-indicator" onclick="toggleCommentDisplay(this.previousElementSibling)">
                                                                    点击展开完整注释
                                                                </div>
                                                            {% else %}
                                                                <div class="comment-text">
                                                                    {{ column.description|default:"" }}
                                                                </div>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 索引信息表格 -->
                {% if indexes %}
                <div class="ant-table-wrapper" style="margin-top: 30px;">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h4 style="display: inline-block; margin: 0; color: #595959; font-size: 16px; font-weight: 500; padding: 8px 20px; background: #f8f9fa; border-radius: 20px; border: 1px solid #e9ecef;">
                            <i class="fas fa-list" style="margin-right: 8px; color: #0D6EFD;"></i>索引信息
                        </h4>
                    </div>
                    <div class="ant-spin-nested-loading">
                        <div class="ant-spin-container">
                            <div class="ant-table ant-table-large ant-table-scroll-position-left">
                                <div class="ant-table-content">
                                    <div class="ant-table-scroll">
                                        <div class="ant-table-body" style="overflow-x: auto;">
                                            <table class="ant-table-fixed">
                                                <colgroup>
                                                    <col style="width: 22%;">
                                                    <col style="width: 15%;">
                                                    <col style="width: 38%;">
                                                    <col style="width: 12%;">
                                                    <col style="width: 13%;">
                                                </colgroup>
                                                <thead class="ant-table-thead">
                                                    <tr>
                                                        <th><span>索引名</span></th>
                                                        <th><span>索引类型</span></th>
                                                        <th><span>字段组合</span></th>
                                                        <th style="text-align: center;"><span>是否唯一</span></th>
                                                        <th><span>注释</span></th>
                                                    </tr>
                                                </thead>
                                                <tbody class="ant-table-tbody">
                                                    {% for index in indexes %}
                                                    <tr class="ant-table-row ant-table-row-level-0">
                                                        <td><div class="index-name-cell">{{ index.name }}</div></td>
                                                        <td><span class="index-type-cell">{{ index.type }}</span></td>
                                                        <td><div class="index-columns-cell">{{ index.column_names }}</div></td>
                                                        <td class="index-unique-cell">
                                                            <span style="color: {% if index.is_unique %}#237804{% else %}#595959{% endif %}; font-weight: 600;">
                                                                {{ index.is_unique|yesno:"是,否" }}
                                                            </span>
                                                        </td>
                                                        <td>{{ index.comment|default:"" }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- 操作按钮 -->
                <div class="export-buttons">
                    <a href="{% url 'data_dictionary:database_detail' database.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回表列表
                    </a>
                    <a href="{% url 'data_dictionary:export_table' database.id table.id %}?type=excel" class="btn btn-success" target="_blank">
                        <i class="fas fa-file-excel"></i> 导出Excel
                    </a>
                    <a href="{% url 'data_dictionary:export_table' database.id table.id %}?type=sql" class="btn btn-primary" target="_blank">
                        <i class="fas fa-code"></i> 导出SQL
                    </a>
                </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关联SQL模板标签页 -->
        <div class="tab-pane fade" id="sql-templates" role="tabpanel" aria-labelledby="sql-templates-tab">
            <div class="ant-tabs-content ant-tabs-content-animated">
                <div role="tabpanel" aria-hidden="false" class="ant-tabs-tabpane">
                    <!-- SQL模板搜索和筛选 -->
                    <div id="sql-templates-toolbar" style="display: none; margin-bottom: 20px;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="sql-search-input" placeholder="搜索SQL模板名称、描述或内容...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="sql-type-filter">
                                    <option value="">适用范围</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="sql-status-filter">
                                    <option value="">所有状态</option>
                                    <option value="complete">完整</option>
                                    <option value="incomplete">不完整</option>
                                    <option value="param">含参数</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="text-muted">显示方式：</span>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <input type="radio" class="btn-check" name="view-mode" id="view-list" value="list" checked>
                                            <label class="btn btn-outline-primary" for="view-list">
                                                <i class="fas fa-list"></i> 列表
                                            </label>
                                            <input type="radio" class="btn-check" name="view-mode" id="view-card" value="card">
                                            <label class="btn btn-outline-primary" for="view-card">
                                                <i class="fas fa-th-large"></i> 卡片
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="text-muted" id="sql-count-info">共 0 个模板</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="sql-templates-content">
                        <div class="text-center" style="padding: 40px;">
                            <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                            <p class="mt-3 text-muted">正在加载关联的SQL模板...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 添加表格行悬停效果
    $('.ant-table-tbody tr').hover(
        function() {
            $(this).addClass('table-row-hover');
        },
        function() {
            $(this).removeClass('table-row-hover');
        }
    );

    // 添加复制功能
    $('.parameter-list .right').click(function() {
        var text = $(this).text().trim();
        if (text && text !== '') {
            navigator.clipboard.writeText(text).then(function() {
                // 可以添加提示信息
                console.log('已复制到剪贴板: ' + text);
            });
        }
    });

    // 添加键盘快捷键
    $(document).keydown(function(e) {
        // Ctrl+E 导出Excel
        if (e.ctrlKey && e.keyCode === 69) {
            e.preventDefault();
            window.open('{% url "data_dictionary:export_table" database.id table.id %}?type=excel');
        }
        // Ctrl+S 导出SQL
        if (e.ctrlKey && e.keyCode === 83) {
            e.preventDefault();
            window.open('{% url "data_dictionary:export_table" database.id table.id %}?type=sql');
        }
    });

    // SQL模板代码复制功能
    $('.sql-template-code').click(function() {
        var sqlText = $(this).text().trim();
        if (sqlText) {
            navigator.clipboard.writeText(sqlText).then(function() {
                // 显示复制成功提示
                var $this = $('.sql-template-code');
                var originalBg = $this.css('background-color');
                $this.css('background-color', '#d4edda');
                setTimeout(function() {
                    $this.css('background-color', originalBg);
                }, 1000);
                console.log('SQL模板已复制到剪贴板');
            }).catch(function(err) {
                console.error('复制失败:', err);
            });
        }
    });

    // 为SQL模板代码添加提示
    $('.sql-template-code').attr('title', '点击复制SQL代码').css('cursor', 'pointer');

    // 标签页切换时的动画效果和异步加载
    $('button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
        var target = $(e.target).attr("data-bs-target");
        $(target).addClass('fade-in');
        setTimeout(function() {
            $(target).removeClass('fade-in');
        }, 300);

        // 如果切换到SQL模板标签页，异步加载内容
        if (target === '#sql-templates') {
            loadSqlTemplates();
        }
    });

    // 异步加载SQL模板
    function loadSqlTemplates() {
        var $content = $('#sql-templates-content');

        // 如果已经加载过，不重复加载
        if ($content.data('loaded')) {
            return;
        }

        // 只在第一次加载时显示加载状态
        $('#templates-count').text('...').show();

        $.get('{% url "data_dictionary:load_sql_templates" table.id %}')
            .done(function(response) {
                if (response.success) {
                    renderSqlTemplates(response.templates, response.count);
                    $content.data('loaded', true);
                } else {
                    $('#templates-count').hide();
                    showError('加载SQL模板失败: ' + response.message);
                }
            })
            .fail(function() {
                $('#templates-count').hide();
                showError('加载SQL模板失败，请稍后重试');
            });
    }

    // 全局变量存储模板数据
    var allTemplates = [];
    var filteredTemplates = [];
    var currentViewMode = 'list';

    // 渲染SQL模板
    function renderSqlTemplates(templates, count) {
        allTemplates = templates;
        filteredTemplates = templates;

        var $content = $('#sql-templates-content');
        var $toolbar = $('#sql-templates-toolbar');

        if (templates.length === 0) {
            $toolbar.hide();
            $content.html(`
                <div class="no-templates">
                    <i class="fas fa-search"></i>
                    <h5>暂无关联的SQL模板</h5>
                    <p>当前表 <strong>{{ table.name }}</strong> 尚未被任何SQL模板使用。</p>
                </div>
            `);
            // 更新标签页数量显示为0
            $('#templates-count').text('0').show();
            $('#sql-count-info').text('共 0 个模板');
            return;
        }

        // 显示工具栏
        $toolbar.show();

        // 初始化筛选选项
        initializeFilters(templates);

        // 渲染模板列表
        renderTemplateList(templates);

        // 更新标签页数量显示
        $('#templates-count').text(count).show();
        $('#sql-count-info').text(`共 ${count} 个模板`);

        // 绑定事件
        bindTemplateEvents();
    }

    // 初始化筛选选项
    function initializeFilters(templates) {
        var scopes = new Set();
        templates.forEach(function(template) {
            if (template.scopes) {
                template.scopes.forEach(function(scope) {
                    scopes.add(scope);
                });
            }
        });

        var $typeFilter = $('#sql-type-filter');
        $typeFilter.html('<option value="">适用范围</option>');
        Array.from(scopes).sort().forEach(function(scope) {
            $typeFilter.append(`<option value="${scope}">${scope}</option>`);
        });
    }

    // 渲染模板列表
    function renderTemplateList(templates) {
        var $content = $('#sql-templates-content');

        if (currentViewMode === 'list') {
            renderListView(templates);
        } else {
            renderCardView(templates);
        }
    }

    // 列表视图
    function renderListView(templates) {
        var $content = $('#sql-templates-content');

        if (templates.length === 0) {
            $content.html(`
                <div class="text-center py-4">
                    <i class="fas fa-search text-muted"></i>
                    <p class="text-muted mt-2">没有找到匹配的SQL模板</p>
                </div>
            `);
            return;
        }

        var html = '<div class="sql-template-list">';

        templates.forEach(function(template) {
            html += renderListItem(template);
        });

        html += '</div>';
        $content.html(html);
    }

    // 卡片视图
    function renderCardView(templates) {
        var $content = $('#sql-templates-content');

        if (templates.length === 0) {
            $content.html(`
                <div class="text-center py-4">
                    <i class="fas fa-search text-muted"></i>
                    <p class="text-muted mt-2">没有找到匹配的SQL模板</p>
                </div>
            `);
            return;
        }

        var html = '';
        templates.forEach(function(template) {
            html += renderSqlTemplateCard(template);
        });

        $content.html(html);
        bindCopyFunctionality();
    }

    // 渲染列表项
    function renderListItem(template) {
        var badges = '';
        if (template.is_param) {
            badges += '<span class="sql-template-badge badge-param">含参数</span>';
        }
        badges += template.is_complete ?
            '<span class="sql-template-badge badge-complete">完整</span>' :
            '<span class="sql-template-badge badge-incomplete">不完整</span>';

        var types = '';
        if (template.types && template.types.length > 0) {
            types = template.types.map(type => `<span class="sql-template-badge badge-type">${type}</span>`).join('');
        }

        return `
            <div class="sql-template-list-item" data-template-id="${template.id}">
                <div class="sql-template-list-header">
                    <h6 class="sql-template-list-title">${template.name}</h6>
                    <div class="sql-template-list-badges">
                        ${badges}
                    </div>
                </div>
                <div class="sql-template-list-meta">
                    <span><i class="fas fa-clock"></i> ${template.create_time || '未知'}</span>
                    ${types ? `<span><i class="fas fa-tags"></i> ${types}</span>` : ''}
                </div>
                ${template.description ? `<div class="sql-template-list-description">${template.description}</div>` : ''}
            </div>
        `;
    }

    // 绑定模板相关事件
    function bindTemplateEvents() {
        // 搜索功能
        $('#sql-search-input').on('input', function() {
            filterTemplates();
        });

        // 分类筛选
        $('#sql-type-filter').on('change', function() {
            filterTemplates();
        });

        // 状态筛选
        $('#sql-status-filter').on('change', function() {
            filterTemplates();
        });

        // 视图模式切换
        $('input[name="view-mode"]').on('change', function() {
            currentViewMode = $(this).val();
            renderTemplateList(filteredTemplates);
        });

        // 列表项点击事件
        $(document).on('click', '.sql-template-list-item', function() {
            var templateId = $(this).data('template-id');
            var template = allTemplates.find(t => t.id === templateId);
            if (template) {
                showTemplateDetail(template);
            }
        });
    }

    // 筛选模板
    function filterTemplates() {
        var searchText = $('#sql-search-input').val().toLowerCase();
        var typeFilter = $('#sql-type-filter').val();
        var statusFilter = $('#sql-status-filter').val();

        filteredTemplates = allTemplates.filter(function(template) {
            // 搜索过滤
            var matchSearch = !searchText ||
                template.name.toLowerCase().includes(searchText) ||
                (template.description && template.description.toLowerCase().includes(searchText)) ||
                (template.sql_content && template.sql_content.toLowerCase().includes(searchText));

            // 适用范围过滤
            var matchType = !typeFilter ||
                (template.scopes && template.scopes.includes(typeFilter));

            // 状态过滤
            var matchStatus = !statusFilter ||
                (statusFilter === 'complete' && template.is_complete) ||
                (statusFilter === 'incomplete' && !template.is_complete) ||
                (statusFilter === 'param' && template.is_param);

            return matchSearch && matchType && matchStatus;
        });

        renderTemplateList(filteredTemplates);
        $('#sql-count-info').text(`共 ${filteredTemplates.length} 个模板`);
    }

    // 显示模板详情
    function showTemplateDetail(template) {
        // 调试：打印模板数据结构
        console.log('Template data:', template);

        // 创建模态框HTML
        var modalHtml = `
            <div class="modal fade" id="templateDetailModal" tabindex="-1" aria-labelledby="templateDetailModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="templateDetailModalLabel">
                                <i class="fas fa-code text-primary"></i> ${template.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            ${renderTemplateDetailContent(template)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        $('#templateDetailModal').remove();

        // 添加新的模态框到页面
        $('body').append(modalHtml);

        // 显示模态框
        var modal = new bootstrap.Modal(document.getElementById('templateDetailModal'));
        modal.show();

        // 绑定复制功能
        bindModalCopyFunctionality();
    }

    // 渲染模板详情内容
    function renderTemplateDetailContent(template) {
        var badges = '';
        if (template.is_param) {
            badges += '<span class="sql-template-badge badge-param">含参数</span>';
        }
        badges += template.is_complete ?
            '<span class="sql-template-badge badge-complete">完整</span>' :
            '<span class="sql-template-badge badge-incomplete">不完整</span>';

        var types = '';
        if (template.types && template.types.length > 0) {
            types = template.types.map(type => `<span class="sql-template-badge badge-type">${type}</span>`).join('');
        }

        var scopes = '';
        if (template.scopes && template.scopes.length > 0) {
            scopes = template.scopes.map(scope => `<span class="sql-template-badge badge-scope">${scope}</span>`).join('');
        }

        // 直接使用原始数据，与卡片视图保持一致
        var hasSQL = template.template && template.template.trim() !== '';

        return `
            <!-- SQL信息 -->
            <div class="sql-template-info mb-4">
                <h6 class="mb-4">
                    <i class="fas fa-info-circle text-primary"></i> SQL信息
                </h6>

                <div class="sql-template-info-item">
                    <div class="sql-template-info-label">状态</div>
                    <div class="sql-template-info-value">${badges}</div>
                </div>

                ${template.description && template.description !== '无描述' ? `
                <div class="sql-template-info-item">
                    <div class="sql-template-info-label">描述</div>
                    <div class="sql-template-info-value template-description">${template.description}</div>
                </div>
                ` : ''}

                ${types ? `
                <div class="sql-template-info-item">
                    <div class="sql-template-info-label">分类</div>
                    <div class="sql-template-info-value">${types}</div>
                </div>
                ` : ''}

                ${scopes ? `
                <div class="sql-template-info-item">
                    <div class="sql-template-info-label">适用范围</div>
                    <div class="sql-template-info-value">${scopes}</div>
                </div>
                ` : ''}

                ${template.create_time ? `
                <div class="sql-template-info-item">
                    <div class="sql-template-info-label">创建时间</div>
                    <div class="sql-template-info-value">${template.create_time}</div>
                </div>
                ` : ''}

                ${template.update_time ? `
                <div class="sql-template-info-item">
                    <div class="sql-template-info-label">更新时间</div>
                    <div class="sql-template-info-value">${template.update_time}</div>
                </div>
                ` : ''}
            </div>

            <!-- 参数信息 -->
            ${template.is_param && (template.param_names || template.param_values || template.param_description) ? `
            <div class="mb-3">
                <h6 class="mb-3">
                    <i class="fas fa-cog text-primary"></i> 参数信息
                </h6>
                ${template.param_names ? `
                <div class="sql-template-info-item">
                    <div class="sql-template-info-label">参数名称</div>
                    <div class="sql-template-info-value">${template.param_names}</div>
                </div>
                ` : ''}
                ${template.param_values ? `
                <div class="sql-template-info-item">
                    <div class="sql-template-info-label">参数值</div>
                    <div class="sql-template-info-value">${template.param_values}</div>
                </div>
                ` : ''}
                ${template.param_description ? `
                <div class="sql-template-info-item">
                    <div class="sql-template-info-label">参数描述</div>
                    <div class="sql-template-info-value">${template.param_description}</div>
                </div>
                ` : ''}
            </div>
            ` : ''}

            <!-- SQL内容 -->
            <div class="mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">
                        <i class="fas fa-code text-primary"></i> SQL内容
                    </h6>
                    ${hasSQL ? `
                    <button class="btn btn-sm btn-outline-primary copy-sql-btn" data-template-id="${template.id}">
                        <i class="fas fa-copy copy-icon"></i>
                        <span class="copy-text">复制SQL</span>
                    </button>
                    ` : ''}
                </div>
                ${hasSQL ? `
                <div class="sql-template-code" style="max-height: 400px; overflow-y: auto; white-space: pre-wrap; word-wrap: break-word; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.3;" id="template-sql-${template.id}">${template.template}</div>
                ` : `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    该模板暂无SQL内容
                </div>
                `}
            </div>
        `;
    }

    // 复制模板SQL
    function copyTemplateSQL(templateId, $button) {
        var template = allTemplates.find(t => t.id == templateId);
        if (template) {
            // 使用与卡片相同的字段名
            var sqlContent = template.template || template.sql_content || template.content || template.sql || '';
            if (sqlContent) {
                navigator.clipboard.writeText(sqlContent).then(function() {
                    // 更新按钮状态
                    updateCopyButtonSuccess($button);
                    // 显示成功提示
                    showCopySuccess();
                }).catch(function(err) {
                    // 降级方案
                    fallbackCopyTextToClipboard(sqlContent, $button);
                });
            } else {
                alert('该模板暂无SQL内容');
                // 恢复按钮状态
                resetCopyButton($button);
            }
        } else {
            // 恢复按钮状态
            resetCopyButton($button);
        }
    }

    // 更新复制按钮为成功状态
    function updateCopyButtonSuccess($button) {
        if ($button && $button.length) {
            $button.removeClass('btn-outline-primary').addClass('btn-success');
            $button.find('.copy-icon').removeClass('fa-copy').addClass('fa-check');
            $button.find('.copy-text').text('已复制');

            // 3秒后恢复原状态
            setTimeout(function() {
                resetCopyButton($button);
            }, 3000);
        }
    }

    // 重置复制按钮状态
    function resetCopyButton($button) {
        if ($button && $button.length) {
            $button.removeClass('btn-success').addClass('btn-outline-primary');
            $button.find('.copy-icon').removeClass('fa-check').addClass('fa-copy');
            $button.find('.copy-text').text('复制SQL');
            $button.prop('disabled', false);
        }
    }

    // 绑定模态框内的复制功能
    function bindModalCopyFunctionality() {
        // 绑定复制按钮事件
        $(document).off('click', '.copy-sql-btn').on('click', '.copy-sql-btn', function() {
            var $btn = $(this);
            var templateId = $btn.data('template-id');

            // 禁用按钮，防止重复点击
            $btn.prop('disabled', true);

            copyTemplateSQL(templateId, $btn);
        });
    }

    // 显示复制成功提示
    function showCopySuccess() {
        // 移除已存在的提示
        $('.copy-success-notification').remove();

        // 创建简单的成功提示
        var notification = $(`
            <div class="copy-success-notification" style="
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 99999;
                background: #28a745;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                font-size: 14px;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 8px;
                animation: slideInRight 0.3s ease-out;
                min-width: 250px;
            ">
                <i class="fas fa-check-circle" style="font-size: 16px;"></i>
                <span>SQL内容已复制到剪贴板</span>
                <button onclick="$(this).parent().remove()" style="
                    background: none;
                    border: none;
                    color: white;
                    font-size: 16px;
                    cursor: pointer;
                    margin-left: auto;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">&times;</button>
            </div>
        `);

        $('body').append(notification);

        // 3秒后自动移除
        setTimeout(function() {
            notification.fadeOut(300, function() {
                notification.remove();
            });
        }, 3000);
    }

    // 降级复制方案
    function fallbackCopyTextToClipboard(text, $button) {
        var textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            updateCopyButtonSuccess($button);
            showCopySuccess();
        } catch (err) {
            alert('复制失败，请手动复制');
            resetCopyButton($button);
        }

        document.body.removeChild(textArea);
    }

    // 渲染单个SQL模板卡片
    function renderSqlTemplateCard(template) {
        var badges = '';
        if (template.is_param) {
            badges += '<span class="sql-template-badge badge-param">含参数</span>';
        }
        badges += template.is_complete ?
            '<span class="sql-template-badge badge-complete">完整</span>' :
            '<span class="sql-template-badge badge-incomplete">不完整</span>';

        var types = '';
        if (template.types && template.types.length > 0) {
            types = `
                <div class="sql-template-info-item">
                    <div class="sql-template-info-label">分类：</div>
                    <div class="sql-template-info-value">
                        ${template.types.map(type => `<span class="sql-template-badge badge-type">${type}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        var scopes = '';
        if (template.scopes && template.scopes.length > 0) {
            scopes = `
                <div class="sql-template-info-item">
                    <div class="sql-template-info-label">适用范围：</div>
                    <div class="sql-template-info-value">
                        ${template.scopes.map(scope => `<span class="sql-template-badge badge-scope">${scope}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        var paramInfo = '';
        if (template.is_param && template.param_names) {
            paramInfo = `
                <div class="mb-3">
                    <strong>参数信息：</strong>
                    <div class="mt-2">
                        <small class="text-muted">
                            参数名称：${template.param_names}<br>
                            ${template.param_values ? '参数值：' + template.param_values + '<br>' : ''}
                            ${template.param_description ? '参数描述：' + template.param_description : ''}
                        </small>
                    </div>
                </div>
            `;
        }

        return `
            <div class="sql-template-card">
                <div class="sql-template-header">
                    <div class="sql-template-title">
                        <span>
                            <i class="fas fa-file-code"></i>
                            ${template.name}
                            ${template.name_en ? '<small>(' + template.name_en + ')</small>' : ''}
                        </span>
                        <div class="sql-template-badges">${badges}</div>
                    </div>
                    ${template.description !== '无描述' ? '<div class="sql-template-meta">' + template.description + '</div>' : ''}
                </div>

                <div class="sql-template-body">
                    <div class="sql-template-info">
                        ${types}
                        ${scopes}
                        <div class="sql-template-info-item">
                            <div class="sql-template-info-label">创建时间：</div>
                            <div class="sql-template-info-value">${template.create_time}</div>
                        </div>
                        <div class="sql-template-info-item">
                            <div class="sql-template-info-label">更新时间：</div>
                            <div class="sql-template-info-value">${template.update_time}</div>
                        </div>
                    </div>

                    ${paramInfo}

                    <div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <strong>SQL模板：</strong>
                            <button class="btn btn-sm btn-outline-primary copy-sql-btn" data-template-id="${template.id}" title="复制SQL代码">
                                <i class="fas fa-copy copy-icon"></i>
                                <span class="copy-text">复制</span>
                            </button>
                        </div>
                        <div class="sql-template-code" id="template-sql-${template.id}">${template.template}</div>
                    </div>
                </div>
            </div>
        `;
    }

    // 绑定复制功能
    function bindCopyFunctionality() {
        // 绑定复制按钮事件
        $('.copy-sql-btn').off('click').on('click', function() {
            var $btn = $(this);
            var templateId = $btn.data('template-id');

            // 禁用按钮，防止重复点击
            $btn.prop('disabled', true);

            copyTemplateSQL(templateId, $btn);
        });

        // 保留原有的点击SQL代码区域复制功能（作为备用）
        $('.sql-template-code').off('click').on('click', function() {
            var sqlText = $(this).text().trim();
            if (sqlText) {
                navigator.clipboard.writeText(sqlText).then(function() {
                    // 显示成功提示
                    showCopySuccess();

                    // 视觉反馈
                    var $this = $('.sql-template-code');
                    var originalBg = $this.css('background-color');
                    $this.css('background-color', '#d4edda');
                    setTimeout(function() {
                        $this.css('background-color', originalBg);
                    }, 1000);
                    console.log('SQL模板已复制到剪贴板');
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    alert('复制失败，请重试');
                });
            }
        });
    }

    // 显示错误信息
    function showError(message) {
        $('#sql-templates-content').html(`
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                ${message}
                <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="loadSqlTemplates()">
                    <i class="fas fa-redo"></i> 重试
                </button>
            </div>
        `);
    }



    // 切换注释显示状态
    window.toggleCommentDisplay = function(element) {
        var $element = $(element);
        var $indicator = $element.siblings('.comment-indicator');

        if ($element.hasClass('comment-short')) {
            // 展开显示
            $element.removeClass('comment-short').addClass('comment-expanded');
            $indicator.text('点击收起');
        } else {
            // 收起显示
            $element.removeClass('comment-expanded').addClass('comment-short');
            $indicator.text('点击展开完整注释');
        }
    };
});
</script>
{% endblock %}
