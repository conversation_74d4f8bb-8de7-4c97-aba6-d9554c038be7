from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    """
    为字段添加数据库注释的辅助函数
    只在Django 4.2+版本中添加db_comment参数
    """
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class PublicColumnInfo(BaseModel):
    """
    字段信息表，存储各个表的字段详细信息，包含约束信息
    """
    table_id = models.IntegerField(
        blank=True,
        null=True,
        verbose_name='所属表ID',
        help_text='所属表ID，用于关联public_table_info.id',
        **_db_comment_kwarg('所属表ID，用于关联public_table_info.id')
    )
    table_name = models.CharField(
        max_length=128,
        default='',
        verbose_name='所属表名称',
        help_text='所属表名称',
        **_db_comment_kwarg('所属表名称')
    )
    name = models.Char<PERSON><PERSON>(
        max_length=128,
        verbose_name='字段名',
        help_text='字段名',
        **_db_comment_kwarg('字段名')
    )
    comment = models.Char<PERSON>ield(
        max_length=512,
        blank=True,
        null=True,
        verbose_name='字段注释',
        help_text='字段注释',
        **_db_comment_kwarg('字段注释')
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='字段描述',
        help_text='字段详细描述',
        **_db_comment_kwarg('字段详细描述')
    )
    data_type = models.CharField(
        max_length=64,
        verbose_name='基础数据类型',
        help_text='基础数据类型，如varchar、int、decimal等',
        **_db_comment_kwarg('基础数据类型，如varchar、int、decimal等')
    )
    type = models.CharField(
        max_length=128,
        verbose_name='完整字段类型',
        help_text='完整字段类型，如varchar(255)、decimal(10,2)等',
        **_db_comment_kwarg('完整字段类型，如varchar(255)、decimal(10,2)等')
    )
    max_length = models.BigIntegerField(
        blank=True,
        null=True,
        verbose_name='最大长度',
        help_text='字段最大长度',
        **_db_comment_kwarg('字段最大长度')
    )
    numeric_precision = models.BigIntegerField(
        blank=True,
        null=True,
        verbose_name='数值精度',
        help_text='数值精度（总位数）',
        **_db_comment_kwarg('数值精度（总位数）')
    )
    numeric_scale = models.BigIntegerField(
        blank=True,
        null=True,
        verbose_name='数值小数位数',
        help_text='数值小数位数',
        **_db_comment_kwarg('数值小数位数')
    )
    is_nullable = models.BooleanField(
        default=True,
        verbose_name='是否允许为空',
        help_text='是否允许为空，TRUE表示可为空',
        **_db_comment_kwarg('是否允许为空，TRUE表示可为空')
    )
    default = models.TextField(
        blank=True,
        null=True,
        verbose_name='默认值',
        help_text='默认值',
        **_db_comment_kwarg('默认值')
    )
    is_auto_increment = models.BooleanField(
        default=False,
        verbose_name='是否自增',
        help_text='是否自增，TRUE表示自增字段',
        **_db_comment_kwarg('是否自增，TRUE表示自增字段')
    )
    is_primary_key = models.BooleanField(
        default=False,
        verbose_name='是否主键',
        help_text='是否主键，TRUE表示主键字段',
        **_db_comment_kwarg('是否主键，TRUE表示主键字段')
    )
    is_unique = models.BooleanField(
        default=False,
        verbose_name='是否唯一约束',
        help_text='是否唯一约束，TRUE表示有唯一约束',
        **_db_comment_kwarg('是否唯一约束，TRUE表示有唯一约束')
    )
    is_indexed = models.BooleanField(
        default=False,
        verbose_name='是否有索引',
        help_text='是否有索引，TRUE表示有索引',
        **_db_comment_kwarg('是否有索引，TRUE表示有索引')
    )
    ordinal_position = models.IntegerField(
        verbose_name='字段位置',
        help_text='字段在表中的位置序号',
        **_db_comment_kwarg('字段在表中的位置序号')
    )

    class Meta:
        db_table = 'public_column_info'
        verbose_name = '字段信息表'
        verbose_name_plural = verbose_name
        unique_together = [['table_id', 'name']]
        indexes = [
            models.Index(fields=['table_id'], name='idx_pub_col_table_id'),
            models.Index(fields=['table_name'], name='idx_pub_col_table_name'),
            models.Index(fields=['is_primary_key'], name='idx_pub_col_primary_key'),
            models.Index(fields=['is_unique'], name='idx_pub_col_unique'),
            models.Index(fields=['is_indexed'], name='idx_pub_col_indexed'),
            models.Index(fields=['data_type'], name='idx_pub_col_data_type'),
            models.Index(fields=['ordinal_position'], name='idx_pub_col_position'),
        ]

    def __str__(self):
        return f"{self.table_name}.{self.name}"


# 动态为 Meta 添加 db_table_comment（仅 Django 4.2+ 支持）
if django.VERSION >= (4, 2):
    PublicColumnInfo.Meta.db_table_comment = '字段信息表，存储各个表的字段详细信息，包含约束信息'
