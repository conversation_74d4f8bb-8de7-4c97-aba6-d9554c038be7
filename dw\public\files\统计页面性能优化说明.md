# 数据字典统计页面性能优化说明

## 问题分析

统计信息页面加载缓慢的主要原因：

### 1. N+1查询问题
原始代码中，对每个数据库都执行多次数据库查询：
```python
for database in database_stats:
    database.table_count = PublicTableInfo.objects.filter(database_id=database.id).count()
    database.active_table_count = PublicTableInfo.objects.filter(
        database_id=database.id,
        is_deprecated=False
    ).count()
    # ... 更多查询
```

### 2. 重复子查询
多次执行相同的子查询，浪费数据库资源。

### 3. 缺乏缓存机制
统计数据没有缓存，每次访问都重新计算。

### 4. 低效的聚合查询
使用多个单独的count()查询而不是一次性聚合。

## 优化方案

### 1. 使用聚合查询优化
```python
# 一次性获取所有数据库的统计信息
database_stats_raw = PublicDatabaseInfo.objects.values(
    'id', 'name', 'type', 'data_source'
).annotate(
    table_count=Count('publictableinfo', distinct=True),
    active_table_count=Count('publictableinfo', filter=Q(publictableinfo__is_deprecated=False), distinct=True)
).order_by('name')
```

### 2. 使用原生SQL优化复杂统计
```python
with connection.cursor() as cursor:
    cursor.execute("""
        SELECT pti.database_id, COUNT(pci.id) as column_count
        FROM public_table_info pti
        LEFT JOIN public_column_info pci ON pci.table_id = pti.id
        GROUP BY pti.database_id
    """)
    column_stats = {row[0]: row[1] for row in cursor.fetchall()}
```

### 3. 添加智能缓存机制
- 实现了缓存管理器，支持Redis不可用时的内存缓存降级
- 缓存时间设置为5分钟，平衡性能和数据实时性
- 提供缓存清除命令

### 4. 数据库索引优化
添加了针对统计查询的复合索引：
- `idx_pub_tbl_db_deprecated`: 优化按数据库和废弃状态的查询
- `idx_pub_col_table_primary`: 优化字段主键统计
- `idx_pub_col_table_unique`: 优化字段唯一约束统计
- 等等

## 性能提升效果

### 预期性能提升
- **查询次数减少**: 从 N+7 次查询减少到 3-4 次查询
- **查询时间减少**: 预计减少 70-90% 的查询时间
- **缓存命中**: 后续访问可直接从缓存获取，响应时间 < 50ms

### 测试命令
```bash
# 测试新方法性能
python manage.py test_statistics_performance

# 测试旧方法性能（对比）
python manage.py test_statistics_performance --old-method

# 清除缓存
python manage.py clear_statistics_cache
```

## 兼容性说明

### Redis不可用时的降级方案
- 当Redis服务不可用时，自动使用内存缓存
- 不会影响页面正常访问
- 日志记录缓存状态，便于监控

### 数据库兼容性
- 优化后的查询兼容MySQL和其他主流数据库
- 使用Django ORM的标准聚合函数
- 原生SQL查询使用标准SQL语法

## 监控和维护

### 性能监控
- 页面显示查询耗时，便于性能监控
- 日志记录缓存使用情况
- 提供性能测试命令

### 缓存管理
- 自动过期机制，避免数据过时
- 手动清除缓存命令
- 内存缓存自动清理过期项

## 后续优化建议

1. **数据库连接池优化**: 确保数据库连接池配置合理
2. **定期统计任务**: 考虑使用Celery定期计算统计数据
3. **分页优化**: 如果数据量继续增长，考虑对统计结果分页
4. **监控告警**: 添加查询时间监控告警机制
