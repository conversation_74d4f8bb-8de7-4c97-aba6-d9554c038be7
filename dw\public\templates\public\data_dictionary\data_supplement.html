{% extends 'public/data_dictionary/base.html' %}
{% load static %}

{% block title %}数据补充 - 数据字典{% endblock %}

{% block extrahead %}
<style>
    .supplement-container {
        padding: 20px;
        background: #f8f9fa;
        min-height: calc(100vh - 120px);
    }
    
    .supplement-header {
        background: white;
        padding: 15px 0;
        margin-bottom: 20px;
        border-bottom: 1px solid #dee2e6;
    }

    .supplement-header h1 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
        color: #495057;
    }

    .supplement-header p {
        margin: 5px 0 0 0;
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .filter-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .filter-section .form-control,
    .filter-section .form-select,
    .filter-section .btn {
        height: 38px;
    }

    .filter-section .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
    }
    
    .nav-tabs .nav-link {
        border: none;
        background: #f8f9fa;
        color: #6c757d;
        margin-right: 5px;
        border-radius: 6px 6px 0 0;
    }
    
    .nav-tabs .nav-link.active {
        background: #0D6EFD;
        color: white;
    }
    
    .data-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .table th {
        background: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .comment-input {
        width: 100%;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 14px;
    }
    
    .comment-input:focus {
        border-color: #0D6EFD;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        outline: none;
    }
    
    .btn-save-batch {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        border-radius: 6px;
        font-weight: 500;
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
    }

    .btn-save-batch:hover {
        background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        color: white;
    }
    
    .stats-badge {
        background: linear-gradient(135deg, #0D6EFD 0%, #0B5ED7 100%);
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .database-badge {
        background: #6f42c1;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .table-name {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #495057;
    }
    
    .column-name {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #0D6EFD;
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    
    .loading-content {
        background: white;
        padding: 30px;
        border-radius: 8px;
        text-align: center;
    }
    
    .alert {
        border: none;
        border-radius: 8px;
    }
    
    .pagination {
        justify-content: center;
        margin-top: 20px;
    }
    
    .pagination .page-link {
        border: none;
        color: #0D6EFD;
        margin: 0 2px;
        border-radius: 6px;
    }
    
    .pagination .page-item.active .page-link {
        background: #0D6EFD;
        border-color: #0D6EFD;
    }
</style>
{% endblock %}

{% block content %}
<div class="supplement-container">
    <!-- 页面标题 -->
    <div class="supplement-header">
        <h1><i class="fas fa-edit"></i> 数据补充</h1>
        <p>处理表中文名缺失和字段中文缺失的数据，完善数据字典信息</p>
    </div>
    
    <!-- 筛选区域 -->
    <div class="filter-section">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">数据库筛选</label>
                <select name="database" class="form-select">
                    <option value="">全部数据库</option>
                    {% for db in databases %}
                    <option value="{{ db.name }}" {% if db.name == database_filter %}selected{% endif %}>
                        {{ db.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-5">
                <label class="form-label">搜索</label>
                <input type="text" name="search" class="form-control"
                       placeholder="搜索表名或字段名" value="{{ search }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary" style="width: 120px;">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="button" class="btn btn-save-batch" style="width: 120px;" onclick="saveAllChanges()">
                    <i class="fas fa-save"></i> 批量保存
                </button>
            </div>
            <input type="hidden" name="tab" value="{{ tab }}">
        </form>
    </div>
    
    <!-- 标签页 -->
    <ul class="nav nav-tabs" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link {% if tab == 'tables' %}active{% endif %}" 
               href="?tab=tables&database={{ database_filter }}&search={{ search }}">
                <i class="fas fa-table"></i> 表中文名缺失
                {% if tab == 'tables' %}
                <span class="stats-badge ms-2">{{ tables_count }} 条</span>
                {% endif %}
            </a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link {% if tab == 'columns' %}active{% endif %}" 
               href="?tab=columns&database={{ database_filter }}&search={{ search }}">
                <i class="fas fa-columns"></i> 字段中文名缺失
                {% if tab == 'columns' %}
                <span class="stats-badge ms-2">{{ columns_count }} 条</span>
                {% endif %}
            </a>
        </li>
    </ul>
    
    <!-- 数据表格 -->
    <div class="data-table">
        {% if tab == 'tables' %}
        <!-- 表中文名缺失 -->
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th style="width: 50px;">序号</th>
                        <th style="width: 120px;">数据库</th>
                        <th style="width: 200px;">表名</th>
                        <th>中文名</th>
                        <th style="width: 100px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for table in tables %}
                    <tr data-table-id="{{ table.id }}">
                        <td>{{ forloop.counter0|add:tables.start_index }}</td>
                        <td><span class="database-badge">{{ table.database_name }}</span></td>
                        <td><span class="table-name">{{ table.name }}</span></td>
                        <td>
                            <input type="text" class="comment-input" 
                                   data-original="{{ table.comment|default:'' }}"
                                   value="{{ table.comment|default:'' }}" 
                                   placeholder="请输入表的中文名">
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="saveTableComment({{ table.id }})">
                                <i class="fas fa-save"></i>
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-2x mb-2"></i><br>
                            没有找到表中文名缺失的数据
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if tables.has_other_pages %}
        <div class="p-3">
            <nav>
                <ul class="pagination">
                    {% if tables.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=tables&page={{ tables.previous_page_number }}&database={{ database_filter }}&search={{ search }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in tables.paginator.page_range %}
                    {% if num == tables.number %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% else %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=tables&page={{ num }}&database={{ database_filter }}&search={{ search }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if tables.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=tables&page={{ tables.next_page_number }}&database={{ database_filter }}&search={{ search }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
        {% else %}
        <!-- 字段中文名缺失 -->
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th style="width: 50px;">序号</th>
                        <th style="width: 120px;">数据库</th>
                        <th style="width: 180px;">表名</th>
                        <th style="width: 150px;">字段名</th>
                        <th style="width: 100px;">数据类型</th>
                        <th>中文名</th>
                        <th style="width: 100px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for column in columns %}
                    <tr data-column-id="{{ column.id }}">
                        <td>{{ forloop.counter0|add:columns.start_index }}</td>
                        <td><span class="database-badge">{{ column.database_name }}</span></td>
                        <td><span class="table-name">{{ column.table_name }}</span></td>
                        <td><span class="column-name">{{ column.name }}</span></td>
                        <td><code>{{ column.data_type }}</code></td>
                        <td>
                            <input type="text" class="comment-input" 
                                   data-original="{{ column.comment|default:'' }}"
                                   value="{{ column.comment|default:'' }}" 
                                   placeholder="请输入字段的中文名">
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="saveColumnComment({{ column.id }})">
                                <i class="fas fa-save"></i>
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-2x mb-2"></i><br>
                            没有找到字段中文名缺失的数据
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if columns.has_other_pages %}
        <div class="p-3">
            <nav>
                <ul class="pagination">
                    {% if columns.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=columns&page={{ columns.previous_page_number }}&database={{ database_filter }}&search={{ search }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in columns.paginator.page_range %}
                    {% if num == columns.number %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% else %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=columns&page={{ num }}&database={{ database_filter }}&search={{ search }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if columns.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?tab=columns&page={{ columns.next_page_number }}&database={{ database_filter }}&search={{ search }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        {% endif %}
    </div>
</div>

<!-- 加载遮罩 -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div>正在保存数据...</div>
    </div>
</div>

<!-- 消息提示区域 -->
<div id="messageContainer" style="position: fixed; top: 80px; right: 20px; z-index: 1050;"></div>
{% endblock %}

{% block extra_js %}
<script>
// 显示消息提示
function showMessage(message, type = 'success') {
    const container = document.getElementById('messageContainer');
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas ${iconClass} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    container.appendChild(alertDiv);

    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 显示/隐藏加载遮罩
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// 保存单个表注释
function saveTableComment(tableId) {
    const row = document.querySelector(`tr[data-table-id="${tableId}"]`);
    const input = row.querySelector('.comment-input');
    const comment = input.value.trim();

    showLoading();

    fetch('{% url "data_dictionary:update_table_comments" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            updates: [{
                id: tableId,
                comment: comment
            }]
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showMessage('表注释保存成功');
            input.setAttribute('data-original', comment);
            // 如果注释不为空，可以考虑从列表中移除该行
            if (comment) {
                row.style.opacity = '0.5';
                setTimeout(() => {
                    row.remove();
                }, 1000);
            }
        } else {
            showMessage(data.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showMessage('网络错误，请重试', 'error');
        console.error('Error:', error);
    });
}

// 保存单个字段注释
function saveColumnComment(columnId) {
    const row = document.querySelector(`tr[data-column-id="${columnId}"]`);
    const input = row.querySelector('.comment-input');
    const comment = input.value.trim();

    showLoading();

    fetch('{% url "data_dictionary:update_column_comments" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            updates: [{
                id: columnId,
                comment: comment
            }]
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showMessage('字段注释保存成功');
            input.setAttribute('data-original', comment);
            // 如果注释不为空，可以考虑从列表中移除该行
            if (comment) {
                row.style.opacity = '0.5';
                setTimeout(() => {
                    row.remove();
                }, 1000);
            }
        } else {
            showMessage(data.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showMessage('网络错误，请重试', 'error');
        console.error('Error:', error);
    });
}

// 批量保存所有修改
function saveAllChanges() {
    const currentTab = '{{ tab }}';
    let updates = [];
    let hasChanges = false;

    if (currentTab === 'tables') {
        // 收集表注释的修改
        document.querySelectorAll('tr[data-table-id]').forEach(row => {
            const input = row.querySelector('.comment-input');
            const original = input.getAttribute('data-original') || '';
            const current = input.value.trim();

            if (original !== current) {
                hasChanges = true;
                updates.push({
                    id: parseInt(row.getAttribute('data-table-id')),
                    comment: current
                });
            }
        });

        if (!hasChanges) {
            showMessage('没有需要保存的修改', 'error');
            return;
        }

        showLoading();

        fetch('{% url "data_dictionary:update_table_comments" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ updates: updates })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showMessage(`成功保存 ${updates.length} 条表注释`);
                // 更新原始值并移除已填写注释的行
                updates.forEach(update => {
                    const row = document.querySelector(`tr[data-table-id="${update.id}"]`);
                    if (row && update.comment) {
                        row.style.opacity = '0.5';
                        setTimeout(() => {
                            row.remove();
                        }, 1000);
                    }
                });
            } else {
                showMessage(data.message || '保存失败', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('网络错误，请重试', 'error');
            console.error('Error:', error);
        });

    } else {
        // 收集字段注释的修改
        document.querySelectorAll('tr[data-column-id]').forEach(row => {
            const input = row.querySelector('.comment-input');
            const original = input.getAttribute('data-original') || '';
            const current = input.value.trim();

            if (original !== current) {
                hasChanges = true;
                updates.push({
                    id: parseInt(row.getAttribute('data-column-id')),
                    comment: current
                });
            }
        });

        if (!hasChanges) {
            showMessage('没有需要保存的修改', 'error');
            return;
        }

        showLoading();

        fetch('{% url "data_dictionary:update_column_comments" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ updates: updates })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showMessage(`成功保存 ${updates.length} 条字段注释`);
                // 更新原始值并移除已填写注释的行
                updates.forEach(update => {
                    const row = document.querySelector(`tr[data-column-id="${update.id}"]`);
                    if (row && update.comment) {
                        row.style.opacity = '0.5';
                        setTimeout(() => {
                            row.remove();
                        }, 1000);
                    }
                });
            } else {
                showMessage(data.message || '保存失败', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }
}

// 回车键保存
document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && e.target.classList.contains('comment-input')) {
        e.preventDefault();
        const row = e.target.closest('tr');
        if (row.hasAttribute('data-table-id')) {
            saveTableComment(parseInt(row.getAttribute('data-table-id')));
        } else if (row.hasAttribute('data-column-id')) {
            saveColumnComment(parseInt(row.getAttribute('data-column-id')));
        }
    }
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 为输入框添加变化提示
    document.querySelectorAll('.comment-input').forEach(input => {
        input.addEventListener('input', function() {
            const original = this.getAttribute('data-original') || '';
            const current = this.value.trim();

            if (original !== current) {
                this.style.borderColor = '#28a745';
                this.style.backgroundColor = '#f8fff9';
            } else {
                this.style.borderColor = '#ced4da';
                this.style.backgroundColor = 'white';
            }
        });
    });
});
</script>
{% endblock %}
