<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}数据字典{% endblock %}</title>

    <!-- DNS预解析和资源预加载 -->
    <link rel="dns-prefetch" href="//cdn.bootcdn.net">
    <link rel="preconnect" href="https://cdn.bootcdn.net" crossorigin>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'public/css/data_dictionary.css' %}">
    
    {% block extrahead %}{% endblock %}
</head>
<body class="data-dictionary-page">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary" style="padding: 0; min-height: 35px; height: 35px;">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'data_dictionary:database_list' %}" style="margin-bottom: 0;">
                <i class="fas fa-book"></i> 数据字典
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'database_list' or request.resolver_match.url_name == 'database_detail' or request.resolver_match.url_name == 'table_detail' %}active{% endif %}"
                           href="{% url 'data_dictionary:database_list' %}">
                            <i class="fas fa-database"></i> 数据库列表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'global_search' %}active{% endif %}"
                           href="{% url 'data_dictionary:global_search' %}">
                            <i class="fas fa-search"></i> 全局搜索
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'statistics' %}active{% endif %}"
                           href="{% url 'data_dictionary:statistics' %}">
                            <i class="fas fa-chart-bar"></i> 统计信息
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'data_supplement' %}active{% endif %}"
                           href="{% url 'data_dictionary:data_supplement' %}">
                            <i class="fas fa-edit"></i> 数据补充
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/dw/admin/" target="_blank">
                            <i class="fas fa-cog"></i> 管理后台
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="text-muted mb-0">
                <i class="fas fa-database"></i> 数据字典系统 - 
                <small>提供完整的数据库元数据管理</small>
            </p>
        </div>
    </footer>

    <!-- jQuery (需要先加载，不使用defer) -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js" defer></script>

    <!-- 页面加载性能监控 -->
    <script>
    // 页面加载完成后的性能监控
    window.addEventListener('load', function() {
        if (window.performance && window.performance.timing) {
            const timing = window.performance.timing;
            const loadTime = timing.loadEventEnd - timing.navigationStart;
            const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;

            // 在开发环境下显示加载时间
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.log(`页面加载时间: ${loadTime}ms, DOM就绪时间: ${domReady}ms`);
            }
        }
    });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
