###----SIMPLEUI 配置-----###
SIMPLEUI_CONFIG = {
    # 是否使用系统默认菜单，关闭以使用自定义权限控制
    'system_keep': False,
    # 用于菜单排序和过滤, 不填此字段为默认排序和全部显示。空列表[] 为全部不显示.
    'menu_display': ['数据字典','基础数据',  '销售数据', '营销数据', '理赔数据', '医疗数据', '其他数据', 'CELERY定时任务', '任务结果',
                     'ApScheduler任务', '认证和授权'],
    'dynamic': True,
    'menus': [
        {
            'name': '首页',
            'icon': 'fa fa-dashboard',
            'url': 'admin:index',
        },
        {
            'name': '数据字典',
            'icon': 'fas fa-book',
            'models': [{
                'name': '数据字典浏览',
                'icon': 'fa fa-eye',
                'url': '/dw/data_dictionary/'
            }, {
                'name': '全局搜索',
                'icon': 'fas fa-search',
                'url': '/dw/data_dictionary/search/'
            }, {
                'name': '数据字典统计',
                'icon': 'fas fa-chart-bar',
                'url': '/dw/data_dictionary/statistics/'
            }, {
                'name': '数据补充',
                'icon': 'fas fa-edit',
                'url': '/dw/data_dictionary/supplement/'
            }, {
                'name': '缓存管理',
                'icon': 'fas fa-memory',
                'url': '/dw/data_dictionary/cache/'
            }, {
                'name': '数据库管理',
                'icon': 'fas fa-database',
                'models': [{
                    'name': '数据库信息',
                    'icon': 'fa fa-server',
                    'url': '/dw/admin/public/publicdatabaseinfo/'
                }, {
                    'name': '表信息管理',
                    'icon': 'fa fa-table',
                    'url': '/dw/admin/public/publictableinfo/'
                }, {
                    'name': '字段信息管理',
                    'icon': 'fa fa-columns',
                    'url': '/dw/admin/public/publiccolumninfo/'
                }, {
                    'name': '索引信息管理',
                    'icon': 'fa fa-key',
                    'url': '/dw/admin/public/publicindexinfo/'
                }]
            }]
        },
        {
            'app': 'public',
            'name': '基础数据',
            'icon': 'fas fa-database',
            'models': [{
                'name': 'SQL模板表',
                'icon': 'fa fa-table',
                'url': 'public/publicsqltemplate/'
            }, {
                'name': '数据统计表',
                'icon': 'fa fa-table',
                'url': 'public/publicstatistics/'
            }, {
                'name': '数据指标主表',
                'icon': 'fa fa-table',
                'url': 'public/publicindicatormain/'
            }, {
                'name': '数据指标结果表',
                'icon': 'fa fa-table',
                'url': 'public/publicindicatordata/'
            }, {
                'name': '地区基础参保信息',
                'icon': 'fa fa-table',
                'url': 'public/publicareabaseinsure/'
            }, {
                'name': '销售目标表',
                'icon': 'fa fa-table',
                'url': 'public/publictarget/'
            }, {
                'name': '数据字典表',
                'icon': 'fa fa-table',
                'url': 'public/systemdict/'
            }, {
                'name': '数据字典值表',
                'icon': 'fa fa-table',
                'url': 'public/systemdictvalue/'
            }, {
                'name': '数据容错表',
                'icon': 'fa fa-table',
                'url': 'public/publicmapping/'
            }, {
                'name': 'SQL类型参数表',
                'icon': 'fa fa-table',
                'url': 'public/publicsqltype/'
            }, {
                'name': 'SQL适用范围表',
                'icon': 'fa fa-table',
                'url': 'public/publicsqlscope/'
            }]
        },
        {
            'app': 'insure',
            'name': '销售数据',
            'icon': 'fas fa-database',
            'models': [{
                'name': '健康险地区参保信息',
                'icon': 'fa fa-table',
                'url': 'insure/insurearea/'
            }, {
                'name': '健康险线上参保信息',
                'icon': 'fa fa-table',
                'url': 'insure/insureonline/'
            }, {
                'name': '健康险代理人参保信息',
                'icon': 'fa fa-table',
                'url': 'insure/insureagent/'
            }, {
                'name': '健康险年龄性别分布表',
                'icon': 'fa fa-table',
                'url': 'insure/insureagesex/'
            }, {
                'name': '健康险渠道手机号码',
                'icon': 'fa fa-table',
                'url': 'insure/insuremobile/'
            }, {
                'name': '健康险PVUV',
                'icon': 'fa fa-table',
                'url': 'insure/insurepvuv/'
            }, {
                'name': '健康险团单数据',
                'icon': 'fa fa-table',
                'url': 'insure/insuregroup/'
            }]
        },
        {
            'app': 'promotion',
            'name': '营销数据',
            'icon': 'fas fa-database',
            'models': [{
                'name': '营销计划安排表',
                'icon': 'fa fa-table',
                'url': 'promotion/promotionplan/'
            }]
        },
        {
            'app': 'claim',
            'name': '理赔数据',
            'icon': 'fas fa-database',
            'models': [{
                'name': '理赔-质子重离子情况',
                'icon': 'fa fa-table',
                'url': 'claim/claimprotonheavyion/'
            },{
                'name': '理赔-月度赔付情况',
                'icon': 'fa fa-table',
                'url': 'claim/claimmonthlypay/'
            },{
                'name': '理赔-责任赔付情况',
                'icon': 'fa fa-table',
                'url': 'claim/claimliabilitypay/'
            },{
                'name': '理赔-保司赔付情况',
                'icon': 'fa fa-table',
                'url': 'claim/claimsellerpay/'
            },{
                'name': '理赔-既往症赔付情况',
                'icon': 'fa fa-table',
                'url': 'claim/claimpreexistingcondition/'
            },{
                'name': '理赔-理赔案件TOP情况',
                'icon': 'fa fa-table',
                'url': 'claim/claimtopcaseinfo/'
            },{
                'name': '理赔-赔付金额分布',
                'icon': 'fa fa-table',
                'url': 'claim/claimamountrangepay/'
            },{
                'name': '理赔-赔付年龄分布情况',
                'icon': 'fa fa-table',
                'url': 'claim/claimagerangepay/'
            },{
                'name': '理赔-赔付地区分布',
                'icon': 'fa fa-table',
                'url': 'claim/claimareapay/'
            },{
                'name': '理赔-险种赔付情况',
                'icon': 'fa fa-table',
                'url': 'claim/claimproductpay/'
            },{
                'name': '理赔-个团赔付情况',
                'icon': 'fa fa-table',
                'url': 'claim/claimgrouppay/'
            },{
                'name': '理赔-申请方式分布',
                'icon': 'fa fa-table',
                'url': 'claim/claimpaytype/'
            },{
                'name': '理赔-性别分布',
                'icon': 'fa fa-table',
                'url': 'claim/claimgenderpay/'
            },{
                'name': '理赔-赔付总览',
                'icon': 'fa fa-table',
                'url': 'claim/claimpayoverview/'
            },{
                'name': '理赔-年龄总览',
                'icon': 'fa fa-table',
                'url': 'claim/claimageoverview/'
            },{
                'name': '理赔-学平险理赔明细',
                'icon': 'fa fa-table',
                'url': 'claim/claimxuepingxian/'
            },{
                'name': '理赔-湖南学平险理赔明细',
                'icon': 'fa fa-table',
                'url': 'claim/claimxuepingxianhn/'
            }]
        },
        {
            'app': 'medical',
            'name': '医疗数据',
            'icon': 'fas fa-database',
            'models': [{
                'name': '医保药品信息基础表',
                'icon': 'fa fa-table',
                'url': 'medical/medicaldrugbase/'
            }, {
                'name': '医保药品省市实体信息表',
                'icon': 'fa fa-table',
                'url': 'medical/medicaldrugentity/'
            }, {
                'name': '医保中草药信息基础表',
                'icon': 'fa fa-table',
                'url': 'medical/medicalchineseherbaldrugbase/'
            }, {
                'name': '医保自制药品基础表',
                'icon': 'fa fa-table',
                'url': 'medical/medicalselfprepareddrugbase/'
            }, {
                'name': '国家谈判药品配备机构目录',
                'icon': 'fa fa-table',
                'url': 'medical/medicalnationalnegotiateddrug/'
            }, {
                'name': '国家谈判药品配备机构供应商',
                'icon': 'fa fa-table',
                'url': 'medical/medicalnationalnegotiateddrugproviders/'
            }, {
                'name': '医疗服务项目分类基础表',
                'icon': 'fa fa-table',
                'url': 'medical/medicalservicebase/'
            }, {
                'name': '医疗服务项目省市实体表',
                'icon': 'fa fa-table',
                'url': 'medical/medicalserviceentity/'
            }, {
                'name': '医保耗材基础表',
                'icon': 'fa fa-table',
                'url': 'medical/medicalsuppliesbase/'
            }, {
                'name': '医保耗材省市实体表',
                'icon': 'fa fa-table',
                'url': 'medical/medicalsuppliesentity/'
            }, {
                'name': '医保耗材注册信息表',
                'icon': 'fa fa-table',
                'url': 'medical/medicalsuppliesregistermessage/'
            }, {
                'name': '医保定点医疗服务机构信息表',
                'icon': 'fa fa-table',
                'url': 'medical/medicaldesignatedproviders/'
            }, {
                'name': '医保疾病诊断信息表',
                'icon': 'fa fa-table',
                'url': 'medical/medicalmedicinediagnosis/'
            }, {
                'name': '医保字段映射表',
                'icon': 'fa fa-table',
                'url': 'medical/medicalfieldmapping/'
            }]
        },
        {
            'app': 'other',
            'name': '其他数据',
            'icon': 'fas fa-database',
            'models': [{
                'name': '医保高铁-宁惠保理赔报表',
                'icon': 'fa fa-table',
                'url': 'other/otherybstatisticnhb/'
            },
                {
                    'name': '保司管理层人员信息',
                    'icon': 'fa fa-table',
                    'url': 'other/othermanagementstaff/'
                }, {
                    'name': '健康险销售sentry回放文件',
                    'icon': 'fa fa-table',
                    'url': 'other/othersentryreplayfiles/'
                }, {
                    'name': 'ST应用信息映射',
                    'icon': 'fa fa-table',
                    'url': 'other/otherstreamlitkeymapping/'
                },
                {
                    'name': 'ST依赖数据表',
                    'icon': 'fa fa-database',
                    'models': [{
                        'name': '理赔-产品信息表',
                        'icon': 'fa fa-table',
                        'url': 'other/otherproduct/'
                    }, {
                        'name': '理赔-审核人员数据范围表',
                        'icon': 'fa fa-table',
                        'url': 'other/otherproductauditperson/'
                    }, {
                        'name': '理赔-产品取数范围',
                        'icon': 'fa fa-table',
                        'url': 'other/otherproductcode/'
                    }, {
                        'name': '销售-宁惠保投保人员医保类型明细',
                        'icon': 'fa fa-table',
                        'url': 'other/otherproductinsuretype/'
                    }, {
                        'name': '销售-投保人员医保类型',
                        'icon': 'fa fa-table',
                        'url': 'other/otherproductmedicaltype/'
                    }, {
                        'name': '理赔-质子重离子赔付数据',
                        'icon': 'fa fa-table',
                        'url': 'other/otherproductproton/'
                    }, {
                        'name': '理赔-责任赔付金额',
                        'icon': 'fa fa-table',
                        'url': 'other/otherproductresponse/'
                    }, {
                        'name': '理赔-审核人员产品工作量指标',
                        'icon': 'fa fa-table',
                        'url': 'other/otherauditpersontarget/'
                    }]
                }, {'name': 'API数据',
                    'icon': 'fa-solid fa-wave-square',
                    'models': [{
                        'name': '销售数据',
                        'icon': 'fa-solid fa-wave-square',
                        'models': [{
                            'name': '滨州护学保数据',
                            'icon': 'fa-solid fa-wave-square',
                            'url': 'https://dw.njzhyl.cn/dw/api/datavhxxinsure/?product_set_code=binzhou_student'
                        },{
                            'name': '滨州护学保二期数据',
                            'icon': 'fa-solid fa-wave-square',
                            'url': 'https://dw.njzhyl.cn/dw/api/datavhxxinsure/?product_set_code=binzhou_studentV2'
                        }, {
                            'name': '宁惠保数据',
                            'icon': 'fa-solid fa-wave-square',
                            'url': 'https://dw.njzhyl.cn/dw/api/datavnhbinsure/?product_set_code=ninghuibaoV5'
                        }, {
                            'name': '日照暖心保数据',
                            'icon': 'fa-solid fa-wave-square',
                            'url': 'https://dw.njzhyl.cn/dw/api/datavnxbinsure/?product_set_code=rizhao_nxbV4'
                        }, {
                            'name': '滨州医惠保数据',
                            'icon': 'fa-solid fa-wave-square',
                            'url': 'https://dw.njzhyl.cn/dw/api/datavbzyhbinsure/?product_set_code=binzhou_yhbV4'
                        }, {
                            'name': '德州惠民保数据',
                            'icon': 'fa-solid fa-wave-square',
                            'url': 'https://dw.njzhyl.cn/dw/api/datavdzhmbinsure/?product_set_code=dezhou_hmbV3'
                        }, {
                            'name': '贵州医惠保数据',
                            'icon': 'fa-solid fa-wave-square',
                            'url': 'https://dw.njzhyl.cn/dw/api/datavghbinsure/?product_set_code=guihuibaoV3'
                        }, {
                            'name': '住院津贴数据',
                            'icon': 'fa-solid fa-wave-square',
                            'url': 'https://dw.njzhyl.cn/dw/api/datavnjdhainsure/?product_set_code=disease_hospital_allowance'
                        }, {
                            'name': '江苏门诊保数据',
                            'icon': 'fa-solid fa-wave-square',
                            'url': 'https://dw.njzhyl.cn/dw/api/datavjsopinsure/?product_set_code=jiangsu_outpatientV1'
                        }, {
                            'name': '南京宁护保数据',
                            'icon': 'fa-solid fa-wave-square',
                            'url': 'https://dw.njzhyl.cn/dw/api/datavnjnhbinsure/?product_set_code=ninghubaoV1'
                        }
                        ]
                    }]
                    }]
        },
        {
            'app': 'auth',
            'name': '认证和授权',
            'icon': 'fas fa-user-shield',
            'models': [{
                'name': '用户',
                'icon': 'fa fa-user',
                'url': 'auth/user/'
            }, {
                'name': '组',
                'icon': 'fa fa-user-group',
                'url': 'auth/group/'
            }]
        },
        {
            'app': 'django_celery_beat',
            'name': 'CELERY定时任务',
            'icon': 'fas fa-list-check',
            'models': [{
                'name': '任务列表',
                'icon': 'fa fa-table-list',
                'url': 'django_celery_beat/periodictask/',
            }, {
                'name': '任务周期',
                'icon': 'fa fa-hourglass',
                'models': [{
                    'name': '时点',
                    'icon': 'fa fa-clock',
                    'url': 'django_celery_beat/clockedschedule/'
                }, {
                    'name': '周期',
                    'icon': 'fa fa-bars',
                    'url': 'django_celery_beat/crontabschedule/'
                }, {
                    'name': '间隔',
                    'icon': 'fa fa-pause',
                    'url': 'django_celery_beat/intervalschedule/'
                }, {
                    'name': '日照',
                    'icon': 'fa fa-sun',
                    'url': 'django_celery_beat/solarschedule/'
                }]
            }]
        },
        {
            'app': 'django_celery_results',
            'name': '任务结果',
            'icon': 'fas fa-sitemap',
            'models': [{
                'name': '普通任务',
                'icon': 'fa fa-rectangle-list',
                'url': 'django_celery_results/taskresult/'
            }, {
                'name': '组任务',
                'icon': 'fa fa-layer-group',
                'url': 'django_celery_results/groupresult/'
            }, {
                'name': 'Flower',
                'icon': 'fas fa-code',
                'url': 'https://dw.njzhyl.cn/flower/',
                # 'url': 'http://127.0.0.1:5555/',
                # 'newTab': True,
            }]
        },
        {
            'app': 'django_apscheduler',
            'name': 'ApScheduler任务',
            'icon': 'fas fa-list-check',
            'models': [{
                'name': '任务',
                'icon': 'fa fa-rectangle-list',
                'url': 'django_apscheduler/djangojob/'
            }, {
                'name': '结果',
                'icon': 'fas fa-sitemap',
                'url': 'django_apscheduler/djangojobexecution/'
            }]
        }
    ]
}

# 去掉主页右侧广告信息
SIMPLEUI_HOME_INFO = False
SIMPLEUI_ANALYSIS = False
