from django.contrib import admin

from insure.models import InsureAgeSex, InsureAgent, InsureArea, InsureOnline, InsureMobile, InsurePvUv, InsureGroup


# Register your models here.


@admin.register(InsureAgeSex)
class InsureAgeSexAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code', 'publish_time', 'sex', 'age_distribution', 'value', 'additional_info', 'create_time',
        'update_time')
    list_filter = ('product_set_code', 'publish_time', 'additional_info')
    search_fields = ('product_set_code',)
    ordering = ['product_set_code', '-publish_time']


@admin.register(InsureAgent)
class InsureAgentAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code', 'publish_time', 'name', 'employee_count', 'average_count',
                    'total_count', 'personal_count', 'group_count', 'insure_ratio', 'position', 'target',
                    'target_ratio', 'today_count', 'yesterday_count', 'week_target', 'week_count',
                    'week_complete_ratio', 'week_target_ratio', 'additional_info', 'create_time', 'update_time')
    list_filter = ('product_set_code', 'publish_time', 'additional_info')
    search_fields = ('product_set_code',)
    ordering = ['product_set_code', '-publish_time']


@admin.register(InsureArea)
class InsureAreaAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code', 'publish_time', 'name', 'total_count',
                    'ratio', 'insure_ratio', 'position', 'today_count',
                    'yesterday_count', 'additional_info', 'create_time', 'update_time')
    list_filter = ('product_set_code', 'publish_time', 'additional_info')
    search_fields = ('product_set_code',)
    ordering = ['product_set_code', '-publish_time']


@admin.register(InsureOnline)
class InsureOnlineAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code', 'publish_time', 'channel_name', 'total_count', 'insure_ratio', 'position',
                    'target', 'target_ratio', 'today_count', 'yesterday_count', 'week_target', 'week_count',
                    'week_complete_ratio', 'week_target_ratio', 'additional_info', 'create_time',
                    'update_time')
    list_filter = ('product_set_code', 'additional_info')
    search_fields = ('product_set_code', 'publish_time')
    ordering = ['-id']


@admin.register(InsureMobile)
class InsureMobileAdmin(admin.ModelAdmin):
    list_display = (
    'id', 'product_short_code', 'channel_name', 'name', 'mobile', 'credential_number', 'additional_info')
    list_filter = ('product_short_code', 'channel_name')
    ordering = ['-id']


@admin.register(InsurePvUv)
class InsurePvUvAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code', 'end_date','source_group', 'source_code', 'type', 'count')
    list_filter = ('product_set_code',)


@admin.register(InsureGroup)
class InsureGroupAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code','seller_name', 'company_name', 'name', 'credential_number',
                    'product_code')
    list_filter = ('product_set_code',)
