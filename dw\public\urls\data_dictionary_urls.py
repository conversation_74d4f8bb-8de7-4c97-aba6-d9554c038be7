"""
数据字典相关URL配置
"""
from django.urls import path
from public.views.data_dictionary_views import (
    database_list,
    database_detail,
    table_detail,
    search_tables,
    search_columns,
    table_structure_json,
    global_search,
    statistics,
    export_table,
    sync_data_dictionary,
    sync_status,
    data_supplement,
    update_table_comments,
    update_column_comments,
    clear_cache,
    cache_status,
    cache_management,
    load_sql_templates
)

app_name = 'data_dictionary'

urlpatterns = [
    # 数据库相关
    path('', database_list, name='database_list'),
    path('database/<int:database_id>/', database_detail, name='database_detail'),
    
    # 表相关
    path('table/<int:table_id>/', table_detail, name='table_detail'),
    path('table/<int:table_id>/sql-templates/', load_sql_templates, name='load_sql_templates'),
    path('table/<int:table_id>/json/', table_structure_json, name='table_structure_json'),
    path('database/<int:database_id>/table/<int:table_id>/export/', export_table, name='export_table'),
    
    # 搜索API
    path('api/search/tables/', search_tables, name='search_tables'),
    path('api/search/columns/', search_columns, name='search_columns'),
    
    # 全局搜索
    path('search/', global_search, name='global_search'),
    
    # 统计页面
    path('statistics/', statistics, name='statistics'),

    # 数据补充
    path('supplement/', data_supplement, name='data_supplement'),
    path('supplement/update-tables/', update_table_comments, name='update_table_comments'),
    path('supplement/update-columns/', update_column_comments, name='update_column_comments'),

    # 数据同步
    path('sync/', sync_data_dictionary, name='sync_data_dictionary'),
    path('sync/status/', sync_status, name='sync_status'),

    # 缓存管理
    path('cache/', cache_management, name='cache_management'),
    path('cache/clear/', clear_cache, name='clear_cache'),
    path('cache/status/', cache_status, name='cache_status'),
]
