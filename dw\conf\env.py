import os

USER = 'shizy'
PASSWORD = 'rab8btd-bfe9kug'
PASSWORD_GZ = "gTlfX5wPUSUEEpXM"
DEFAULT_PASSWORD = 'WKG9yar1btm9xjq_rjf'

DATABASES = {
    # 'default': {
    #     'ENGINE': 'django.db.backends.mysql',
    #     'NAME': 'dw',
    #     'HOST': '**************',
    #     'PORT': 3306,
    #     'USER': 'root',
    #     'PASSWORD': DEFAULT_PASSWORD,
    #     'OPTIONS': {
    #         'charset': 'utf8mb4',
    #         'use_unicode': True,
    #     }
    # },
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'dw',
        'HOST': 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
        'PORT': 3306,
        'USER': USER,
        'PASSWORD': PASSWORD,
    },
    'local_default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'dw',
        'HOST': '**************',
        'PORT': 3306,
        'USER': 'root',
        'PASSWORD': DEFAULT_PASSWORD,
    },
    'dw': {
        "ENGINE": "django.db.backends.mysql",  # 本地用，用于写入正式数据库
        "NAME": 'dw',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'jkx': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'jkx_slave',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    # 'jkx': {
    #         "ENGINE": "django.db.backends.mysql",
    #         "NAME": 'jkx', # 测试环境用
    #         "USER": USER,
    #         "PASSWORD": PASSWORD,
    #         "HOST": 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
    #         "PORT": '3306',
    #     },
    'nhb': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'nhb-claim_slave',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'claim': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'claim_slave',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'jkx_ghb': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'jkx_slave',
        "USER": USER,
        "PASSWORD": PASSWORD_GZ,
        "HOST": 'rm-2zev32g9b4fvb27o3fo.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'ghb': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'insurance-claim-prod',
        "USER": USER,
        "PASSWORD": PASSWORD_GZ,
        "HOST": 'jkx-ghb-public.mysql.polardb.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'umami': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'umami',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'polardb-ai.rwlb.rds.aliyuncs.com',
        "PORT": '3306',
    }
}

# ================================================= #
# ******** redis配置，用于缓存数据  ******** #
# ================================================= #
REDIS_PASSWORD = '2279856'
REDIS_HOST = '127.0.0.1'
REDIS_URL = f'redis://:{REDIS_PASSWORD or ""}@{REDIS_HOST}:6379/1'
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}

###----Celery redis 配置-----###
# Broker配置，使用Redis作为消息中间件
CELERY_BROKER_BACKEND = 'redis'
# redis沒有設置密碼的
CELERY_REDIS_PASSWORD = '2279856'
CELERY_REDIS_HOST = '127.0.0.1'
CELERY_BROKER_URL = f'redis://:{CELERY_REDIS_PASSWORD or ""}@{CELERY_REDIS_HOST}:6379/0'
# 設置密碼用下面的
# CELERY_BROKER_URL = 'redis://:password@127.0.0.1:6379/0'
